"""
模块4：区域流转器 (RegionTransitioner)
只做一件事：处理跨区域的ID流转

核心原则：
1. 同一张物理牌在不同区域流转，保持相同的卡牌序号
2. 正确流转：1二 → 1二 → 1二 → 1二（ID不变，只有group_id变化）
3. ID格式：{卡牌序号}{牌面}（不包含区域信息，区域信息存储在group_id中）
"""

from typing import Dict, List, Any, Tuple, Optional, Set
from dataclasses import dataclass
import logging

logger = logging.getLogger(__name__)

@dataclass
class TransitionResult:
    """区域流转结果"""
    transitioned_cards: List[Dict[str, Any]]
    new_cards: List[Dict[str, Any]]
    statistics: Dict[str, Any]

class RegionTransitioner:
    """区域流转器 - 只负责处理跨区域的ID流转"""
    
    def __init__(self):
        # 卡牌流转历史：{base_id: {区域: 完整ID}}
        # 例如：{"1二": {1: "1二1", 2: "1二2", 4: "1二4"}}
        self.card_transition_history: Dict[str, Dict[int, str]] = {}
        
        # 区域流转路径定义 - 🔧 根据GAME_RULES.md规范重构
        self.transition_paths = {
            # 观战方流转路径
            1: [2, 6, 14, 15],      # 手牌 → 调整 → 吃碰区 → 赢方区域  # 🔧 修正：1→2, 1→6, 1→14, 1→15
            2: [1, 4],              # 调整 → 手牌 → 打牌  # 🔧 修正：2→1, 2→4
            3: [1, 5, 6, 16],          # 抓牌 → 弃牌 → 吃碰区  # 🔧 修正：3→5, 3→6, 3→16
            4: [5, 6, 16],          # 打牌 → 弃牌 → 吃碰区  # 🔧 修正：4→5, 4→6, 4→16

            # 对战方流转路径
            7: [6, 16, 9],          # 抓牌 → 观战方吃碰区 → 对战方吃碰区 → 弃牌  # 🔧 修正：7→6, 7→16, 7→9
            8: [6, 16, 9],          # 打牌 → 观战方吃碰区 → 对战方吃碰区 → 弃牌  # 🔧 修正：8→6, 8→16, 8→9
            9: [11],                # 弃牌 → 最终弃牌
            10: [11],               # 打出 → 弃牌

            # 吃碰区流转路径
            6: [14, 15],            # 观战方吃碰区 → 赢方区域  # 🔧 修正：6→14, 6→15
            16: [14, 15],           # 对战方吃碰区 → 赢方区域  # 🔧 修正：16→14, 16→15

            # 其他区域流转路径
            17: [14, 15],           # 区域17 → 赢方区域  # 🔧 新增：17→14, 17→15
        }

        # 🔧 特殊流转路径定义（直接跨区域流转）- 根据GAME_RULES.md规范
        self.special_transition_paths = {
            # 观战方特殊流转
            1: [2, 4, 6, 14, 15],   # 手牌直接到调整、打牌、吃碰区、赢方区域  # 🔧 1→2, 1→4, 1→6, 1→14, 1→15
            2: [1],                 # 调整直接到手牌  # 🔧 2→1（删除不存在的2→4）
            3: [1, 5, 6, 16],       # 抓牌直接到手牌、弃牌、吃碰区  # 🔧 修复：3→1, 3→5, 3→6, 3→16
            4: [5, 16],             # 打牌直接到弃牌区、对战方吃碰区  # 🔧 4→5, 4→16（删除不存在的4→6）

            # 对战方特殊流转
            7: [6, 16, 9],          # 抓牌直接到观战方吃碰区、对战方吃碰区、弃牌  # 🔧 7→6, 7→16, 7→9
            8: [6, 16, 9],          # 打牌直接到观战方吃碰区、对战方吃碰区、弃牌  # 🔧 8→6, 8→16, 8→9

            # 吃碰区特殊流转
            6: [14, 15],            # 观战方吃碰区直接到赢方区域  # 🔧 6→14, 6→15
            16: [14, 15],           # 对战方吃碰区直接到赢方区域  # 🔧 16→14, 16→15
            17: [14, 15],           # 区域17直接到赢方区域  # 🔧 17→14, 17→15
        }
        
        # 流转统计
        self.transition_stats = {
            "total_frames": 0,
            "total_cards_processed": 0,
            "total_transitioned": 0,
            "total_new": 0,
            "transition_paths_used": {}
        }
        
        logger.info("区域流转器初始化完成")
    
    def process_transitions(self, current_cards: List[Dict[str, Any]],
                           previous_cards: List[Dict[str, Any]] = None) -> TransitionResult:
        """处理区域流转逻辑"""
        self.transition_stats["total_frames"] += 1
        self.transition_stats["total_cards_processed"] += len(current_cards)

        logger.info(f"开始处理区域流转，当前帧{len(current_cards)}张卡牌")

        # 🔧 新增：处理特殊的流转到区域4（观战方打牌区）
        if previous_cards:
            current_cards = self._handle_special_transitions_to_4(current_cards, previous_cards)

        # 🔧 新增：处理特殊的7→16区域流转（对战方抓牌区→吃碰区）
        if previous_cards:
            current_cards = self._handle_special_7_to_16_transition(current_cards, previous_cards)

        # 🔧 删除：区域6流转逻辑已迁移到SimpleInheritor中统一处理
        # 避免重复处理和模块间冲突

        # 🔧 新增：处理特殊的4→5流转（观战方打牌区→弃牌区）
        if previous_cards:
            current_cards = self._handle_special_4_to_5_transition(current_cards, previous_cards)

        transitioned_cards = []
        new_cards = []

        for card in current_cards:
            # 提取基础ID（卡牌序号+牌面）
            base_id = self._extract_base_id(card.get('twin_id', ''))
            current_region = card['group_id']

            if base_id and self._can_transition(base_id, current_region):
                # 可以流转
                transitioned_card = self._transition_card(card, base_id, current_region)
                transitioned_cards.append(transitioned_card)
                self.transition_stats["total_transitioned"] += 1

                logger.debug(f"区域流转: {base_id} → 区域{current_region}")
            else:
                # 无法流转，作为新卡牌
                new_cards.append(card)
                self.transition_stats["total_new"] += 1
                logger.debug(f"新卡牌: 区域{current_region}, 标签{card['label']}")

        # 生成统计信息
        statistics = self._generate_statistics(len(transitioned_cards), len(new_cards))

        logger.info(f"区域流转完成: 流转{len(transitioned_cards)}张, 新增{len(new_cards)}张")

        return TransitionResult(
            transitioned_cards=transitioned_cards,
            new_cards=new_cards,
            statistics=statistics
        )

    def _handle_special_7_to_16_transition(self, current_cards: List[Dict[str, Any]],
                                          previous_cards: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        处理特殊的跨区域流转到区域16

        支持的流转路径（按优先级）：
        1. 7→16: 对战方抓牌区→对战方吃碰区
        2. 3→16: 观战方抓牌区→对战方吃碰区
        3. 4→16: 观战方打牌区→对战方吃碰区

        注意：已移除16→16内部继承逻辑，区域16内部继承由SimpleInheritor处理
        """
        # 获取前一帧各源区域的卡牌
        region_7_cards = [card for card in previous_cards if card.get('group_id') == 7]   # 对战方抓牌区
        region_3_cards = [card for card in previous_cards if card.get('group_id') == 3]   # 观战方抓牌区
        region_4_cards = [card for card in previous_cards if card.get('group_id') == 4]   # 观战方打牌区

        # 获取当前帧区域16的卡牌
        region_16_cards = [card for card in current_cards if card.get('group_id') == 16]

        if not region_16_cards:
            return current_cards

        # 处理区域16中的明牌（非暗牌）
        updated_cards = []
        for card in current_cards:
            if card.get('group_id') == 16 and card.get('label') != '暗':
                # 🔧 第一优先级：7→16流转，实际执行ID继承
                matching_card_7 = self._find_matching_card_in_region_7(card['label'], region_7_cards)
                if matching_card_7:
                    # 实际执行ID继承
                    updated_card = card.copy()
                    source_id = self._get_digital_twin_id(matching_card_7)

                    if source_id:
                        # 设置继承的ID到多个字段以确保兼容性
                        updated_card['twin_id'] = source_id
                        if 'attributes' not in updated_card:
                            updated_card['attributes'] = {}
                        updated_card['attributes']['digital_twin_id'] = source_id
                        updated_card['inherited'] = True
                        updated_card['transition_source'] = '7→16'
                        updated_card['from_region_7'] = True
                        updated_card['source_card_id'] = source_id

                        logger.info(f"🔄 区域7→16流转继承: {card['label']} 继承ID {source_id}")
                        updated_cards.append(updated_card)
                    else:
                        logger.warning(f"⚠️ 区域7卡牌缺少数字孪生ID: {matching_card_7.get('label', 'unknown')}")
                        # 如果源卡牌没有ID，标记但不继承
                        updated_card['from_region_7'] = True
                        updated_card['source_card_id'] = None
                        logger.info(f"🔄 区域7→16流转标记: {card['label']} 来源ID None")
                        updated_cards.append(updated_card)
                else:
                    # 🔧 第二优先级：3→16流转（观战方抓牌区→对战方吃碰区）
                    matching_card_3 = self._find_matching_card_in_region_3(card['label'], region_3_cards)
                    if matching_card_3:
                        # 实际执行ID继承
                        updated_card = card.copy()
                        source_id = self._get_digital_twin_id(matching_card_3)

                        if source_id:
                            # 🔧 区域3→16流转：标记为流转，只有第一张卡牌标记释放源ID
                            updated_card['inherited'] = True
                            updated_card['transition_source'] = '3→16'
                            updated_card['from_region_3'] = True
                            updated_card['source_card_id'] = source_id
                            updated_card['base_id'] = self._extract_base_label(source_id)  # 提取基础标签（如"二"）

                            # 检查是否是第一张相同标签的流转卡牌
                            same_label_count = len([c for c in updated_cards if c.get('from_region_3') and c.get('label') == card['label']])
                            if same_label_count == 0:  # 第一张相同标签的流转卡牌
                                updated_card['release_source_id'] = True
                                logger.info(f"🔄 区域3→16流转标记: {card['label']} 来源ID {source_id}，将释放并批量重新分配")
                            else:
                                logger.info(f"🔄 区域3→16流转标记: {card['label']} 来源ID {source_id}，等待批量分配")

                            updated_cards.append(updated_card)
                        else:
                            logger.warning(f"⚠️ 区域3卡牌缺少数字孪生ID: {matching_card_3.get('label', 'unknown')}")
                            # 如果源卡牌没有ID，标记但不继承
                            updated_card['from_region_3'] = True
                            updated_card['source_card_id'] = None
                            logger.info(f"🔄 区域3→16流转标记: {card['label']} 来源ID None")
                            updated_cards.append(updated_card)
                    else:
                        # 🔧 第三优先级：4→16流转（观战方打牌区→对战方吃碰区）
                        matching_card_4 = self._find_matching_card_in_region_4(card['label'], region_4_cards)
                        if matching_card_4:
                            # 🔧 区域4→16流转：标记为流转，使用与3→16相同的批量重新分配机制
                            updated_card = card.copy()
                            source_id = self._get_digital_twin_id(matching_card_4)

                            if source_id:
                                updated_card['inherited'] = True
                                updated_card['transition_source'] = '4→16'
                                updated_card['from_region_4'] = True
                                updated_card['source_card_id'] = source_id
                                updated_card['base_id'] = self._extract_base_label(source_id)  # 提取基础标签（如"六"）

                                # 检查是否是第一张相同标签的流转卡牌
                                same_label_count = len([c for c in updated_cards if c.get('from_region_4') and c.get('label') == card['label']])
                                if same_label_count == 0:  # 第一张相同标签的流转卡牌
                                    updated_card['release_source_id'] = True
                                    logger.info(f"🔄 区域4→16流转标记: {card['label']} 来源ID {source_id}，将释放并批量重新分配")
                                else:
                                    logger.info(f"🔄 区域4→16流转标记: {card['label']} 来源ID {source_id}，等待批量分配")

                                updated_cards.append(updated_card)
                            else:
                                logger.warning(f"⚠️ 区域4卡牌缺少数字孪生ID: {matching_card_4.get('label', 'unknown')}")
                                # 如果源卡牌没有ID，标记但不继承
                                updated_card['from_region_4'] = True
                                updated_card['source_card_id'] = None
                                logger.info(f"🔄 区域4→16流转标记: {card['label']} 来源ID None")
                                updated_cards.append(updated_card)
                        else:
                            # 无法从区域7、3、4继承，保持原卡牌（将由SimpleInheritor处理继承）
                            logger.info(f"📝 区域16卡牌: {card['label']} (无法从区域7、3、4继承，交由SimpleInheritor处理)")
                            updated_cards.append(card)
            else:
                updated_cards.append(card)

        return updated_cards

    def _find_matching_card_in_region_7(self, label: str, region_7_cards: List[Dict[str, Any]]) -> Optional[Dict[str, Any]]:
        """在区域7中查找相同标签的卡牌"""
        # 提取当前卡牌的基础牌面（去除序号前缀）
        current_base_label = self._extract_base_label(label)

        for card in region_7_cards:
            # 提取区域7卡牌的基础牌面
            card_base_label = self._extract_base_label(card.get('label', ''))
            if card_base_label == current_base_label:
                return card
        return None

    def _find_matching_card_in_region_3(self, label: str, region_3_cards: List[Dict[str, Any]]) -> Optional[Dict[str, Any]]:
        """在区域3中查找相同标签的卡牌（复用区域7的匹配逻辑）"""
        # 提取当前卡牌的基础牌面（去除序号前缀）
        current_base_label = self._extract_base_label(label)

        for card in region_3_cards:
            # 提取区域3卡牌的基础牌面
            card_base_label = self._extract_base_label(card.get('label', ''))
            if current_base_label == card_base_label:
                return card
        return None

    def _find_matching_card_in_region_4(self, label: str, region_4_cards: List[Dict[str, Any]]) -> Optional[Dict[str, Any]]:
        """在区域4中查找相同标签的卡牌（复用区域7的匹配逻辑）"""
        # 提取当前卡牌的基础牌面（去除序号前缀）
        current_base_label = self._extract_base_label(label)

        for card in region_4_cards:
            # 提取区域4卡牌的基础牌面
            card_base_label = self._extract_base_label(card.get('label', ''))
            if current_base_label == card_base_label:
                return card
        return None



    def _extract_base_label(self, label: str) -> str:
        """提取基础牌面标签（去除数字前缀）

        例如：
        "1五" -> "五"
        "五" -> "五"
        "2三" -> "三"
        """
        if not label:
            return ""

        # 如果标签以数字开头，去除数字前缀
        if len(label) > 1 and label[0].isdigit():
            return label[1:]

        # 否则返回原标签
        return label

    def _get_digital_twin_id(self, card: Dict[str, Any]) -> Optional[str]:
        """获取卡牌的数字孪生ID（支持多种字段格式）

        Args:
            card: 卡牌数据

        Returns:
            数字孪生ID字符串，如果找不到则返回None
        """
        # 优先检查 attributes.digital_twin_id
        if 'attributes' in card and isinstance(card['attributes'], dict):
            twin_id = card['attributes'].get('digital_twin_id')
            if twin_id:
                return twin_id

        # 检查直接的 twin_id 字段
        twin_id = card.get('twin_id')
        if twin_id:
            return twin_id

        # 检查直接的 digital_twin_id 字段
        twin_id = card.get('digital_twin_id')
        if twin_id:
            return twin_id

        return None

    def _extract_base_id(self, twin_id: str) -> Optional[str]:
        """提取基础ID（卡牌序号+牌面）- 根据新的ID格式标准"""
        if not twin_id:
            return None

        # 跳过虚拟牌
        if twin_id.startswith('虚拟'):
            return None

        # 处理不同格式的ID
        if '暗' in twin_id:
            # 暗牌格式：1二暗 → 1二
            return twin_id.replace('暗', '')
        else:
            # 明牌格式：1二 → 1二（已经是基础ID）
            return twin_id
    
    def _can_transition(self, base_id: str, current_region: int) -> bool:
        """检查是否可以进行区域流转"""
        if base_id not in self.card_transition_history:
            return False
        
        # 检查这张牌是否曾经在其他区域出现过
        history = self.card_transition_history[base_id]
        
        # 如果当前区域已经存在，说明是继承
        if current_region in history:
            return True
        
        # 检查是否是合理的流转路径
        for prev_region in history.keys():
            if self._is_valid_transition_path(prev_region, current_region):
                return True
        
        return False
    
    def _is_valid_transition_path(self, from_region: int, to_region: int) -> bool:
        """检查是否是有效的流转路径"""
        # 检查常规流转路径
        if from_region in self.transition_paths:
            if to_region in self.transition_paths[from_region]:
                return True

        # 🔧 检查特殊流转路径（直接跨区域流转）
        if from_region in self.special_transition_paths:
            if to_region in self.special_transition_paths[from_region]:
                logger.info(f"检测到特殊流转路径: 区域{from_region} → 区域{to_region}")
                return True

        return False
    
    def _transition_card(self, card: Dict[str, Any], base_id: str, current_region: int) -> Dict[str, Any]:
        """执行卡牌区域流转 - 根据设计文档，ID格式不包含区域信息"""
        transitioned_card = card.copy()

        # 更新流转历史
        if base_id not in self.card_transition_history:
            self.card_transition_history[base_id] = {}

        # 生成正确格式的ID（不包含区域信息）
        if '暗' in card.get('twin_id', ''):
            # 暗牌格式：{序号}{牌面}暗（不包含区域信息）
            new_twin_id = f"{base_id}暗"
        else:
            # 明牌格式：{序号}{牌面}（不包含区域信息）
            new_twin_id = base_id

        # 更新卡牌信息
        transitioned_card['twin_id'] = new_twin_id
        transitioned_card['transitioned'] = True
        transitioned_card['base_id'] = base_id
        transitioned_card['previous_regions'] = list(self.card_transition_history[base_id].keys())

        # 记录流转历史（使用正确格式的ID）
        self.card_transition_history[base_id][current_region] = new_twin_id

        # 更新流转路径统计
        for prev_region in self.card_transition_history[base_id].keys():
            if prev_region != current_region:
                path_key = f"{prev_region}→{current_region}"
                if path_key not in self.transition_stats["transition_paths_used"]:
                    self.transition_stats["transition_paths_used"][path_key] = 0
                self.transition_stats["transition_paths_used"][path_key] += 1

        return transitioned_card
    
    def register_new_card(self, card: Dict[str, Any]):
        """注册新卡牌到流转历史"""
        twin_id = card.get('twin_id', '')
        base_id = self._extract_base_id(twin_id)
        current_region = card['group_id']
        
        if base_id:
            if base_id not in self.card_transition_history:
                self.card_transition_history[base_id] = {}
            
            self.card_transition_history[base_id][current_region] = twin_id
            logger.debug(f"注册新卡牌到流转历史: {base_id} 在区域{current_region}")
    
    def _generate_statistics(self, transitioned_count: int, new_count: int) -> Dict[str, Any]:
        """生成统计信息"""
        total_current = transitioned_count + new_count
        transition_rate = transitioned_count / total_current if total_current > 0 else 0
        
        # 计算总体流转率
        total_transition_rate = (
            self.transition_stats["total_transitioned"] / 
            self.transition_stats["total_cards_processed"]
            if self.transition_stats["total_cards_processed"] > 0 else 0
        )
        
        return {
            "current_frame": {
                "transitioned": transitioned_count,
                "new": new_count,
                "total": total_current,
                "transition_rate": transition_rate
            },
            "overall": {
                "total_frames": self.transition_stats["total_frames"],
                "total_cards": self.transition_stats["total_cards_processed"],
                "total_transitioned": self.transition_stats["total_transitioned"],
                "total_new": self.transition_stats["total_new"],
                "overall_transition_rate": total_transition_rate
            },
            "transition_paths": self.transition_stats["transition_paths_used"].copy(),
            "active_cards": len(self.card_transition_history)
        }
    
    def reset_transition_history(self):
        """重置流转历史（用于新局开始）"""
        self.card_transition_history.clear()
        self.transition_stats = {
            "total_frames": 0,
            "total_cards_processed": 0,
            "total_transitioned": 0,
            "total_new": 0,
            "transition_paths_used": {}
        }
        logger.info("区域流转历史已重置")
    
    def get_transition_rate(self) -> float:
        """获取当前流转率"""
        if self.transition_stats["total_cards_processed"] == 0:
            return 0.0
        return (
            self.transition_stats["total_transitioned"] / 
            self.transition_stats["total_cards_processed"]
        )
    
    def get_card_history(self, base_id: str) -> Dict[int, str]:
        """获取指定卡牌的流转历史"""
        return self.card_transition_history.get(base_id, {})

    def _handle_special_transitions_to_6(self, current_cards: List[Dict[str, Any]],
                                        previous_cards: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        处理特殊的跨区域流转到区域6（观战方吃碰区）

        支持的流转路径（平等处理，防止重复匹配）：
        1. 1→6: 观战方手牌区→观战方吃碰区（跑牌）
        2. 3→6: 观战方抓牌区→观战方吃碰区（吃牌）
        3. 4→6: 观战方打牌区→观战方吃碰区（吃牌）
        4. 7→6: 对战方抓牌区→观战方吃碰区（吃牌）
        5. 8→6: 对战方打牌区→观战方吃碰区（吃牌）
        """
        # 获取前一帧各源区域的卡牌
        region_1_cards = [card for card in previous_cards if card.get('group_id') == 1]   # 观战方手牌区
        region_3_cards = [card for card in previous_cards if card.get('group_id') == 3]   # 观战方抓牌区
        region_4_cards = [card for card in previous_cards if card.get('group_id') == 4]   # 观战方打牌区
        region_7_cards = [card for card in previous_cards if card.get('group_id') == 7]   # 对战方抓牌区
        region_8_cards = [card for card in previous_cards if card.get('group_id') == 8]   # 对战方打牌区

        # 获取当前帧区域6的卡牌
        region_6_cards = [card for card in current_cards if card.get('group_id') == 6]

        if not region_6_cards:
            return current_cards

        # 🔧 增强调试：详细记录前一帧各源区域的卡牌情况
        logger.info(f"🔧 区域6流转源分析（删除不存在的2→6和4→6流转）:")
        logger.info(f"  区域1（手牌区）: {len(region_1_cards)}张")
        for card in region_1_cards:
            card_id = self._get_digital_twin_id(card)
            logger.info(f"    - {card.get('label')} (ID: {card_id})")

        logger.info(f"  区域3（抓牌区）: {len(region_3_cards)}张")
        for card in region_3_cards:
            card_id = self._get_digital_twin_id(card)
            logger.info(f"    - {card.get('label')} (ID: {card_id})")

        logger.info(f"  区域7（对战方抓牌区）: {len(region_7_cards)}张")
        for card in region_7_cards:
            card_id = self._get_digital_twin_id(card)
            logger.info(f"    - {card.get('label')} (ID: {card_id})")

        logger.info(f"  区域8（对战方打牌区）: {len(region_8_cards)}张")
        for card in region_8_cards:
            card_id = self._get_digital_twin_id(card)
            logger.info(f"    - {card.get('label')} (ID: {card_id})")

        logger.info(f"  当前帧区域6（吃碰区）: {len(region_6_cards)}张")
        for card in region_6_cards:
            logger.info(f"    - {card.get('label')} (位置: {card.get('bbox', [0,0,0,0])[1]})")

        # 🔧 新增：按基础标签分组处理，防止重复匹配
        updated_cards = []

        # 按基础标签分组区域6的卡牌
        cards_by_base_label = {}
        for card in region_6_cards:
            if card.get('label') != '暗':  # 只处理明牌
                base_label = self._extract_base_label(card.get('label', ''))
                if base_label not in cards_by_base_label:
                    cards_by_base_label[base_label] = []
                cards_by_base_label[base_label].append(card)

        # 对每个基础标签组进行流转处理
        for base_label, target_cards in cards_by_base_label.items():
            logger.info(f"🎯 处理基础标签 '{base_label}': {len(target_cards)}张目标卡牌")

            # 🔧 收集所有源区域的相同基础标签卡牌
            all_source_cards = []

            # 🔧 修正：按优先级顺序收集源卡牌，删除不存在的4→6流转
            for source_region in [1, 3, 7, 8]:  # 删除4（4→6不存在）
                region_cards = {
                    1: region_1_cards,
                    3: region_3_cards,
                    7: region_7_cards,
                    8: region_8_cards
                }[source_region]

                # 查找相同基础标签的卡牌
                matching_cards = []
                for card in region_cards:
                    card_base_label = self._extract_base_label(card.get('label', ''))
                    if card_base_label == base_label:
                        matching_cards.append((card, source_region))

                if matching_cards:
                    # 🔧 修复：按ID倒序排序，优先选择较大的ID（如2八优先于1八）
                    def extract_id_number(card_tuple):
                        card, _ = card_tuple
                        twin_id = self._get_digital_twin_id(card) or ''
                        if not twin_id or twin_id.startswith('虚拟'):
                            return 0
                        try:
                            return int(twin_id[0])
                        except (ValueError, IndexError):
                            return 0

                    # 🔧 增强调试：排序前记录原始顺序
                    logger.info(f"  区域{source_region}→6流转候选卡牌（排序前）:")
                    for i, (card, _) in enumerate(matching_cards):
                        card_id = self._get_digital_twin_id(card)
                        logger.info(f"    {i+1}. {card.get('label')} (ID: {card_id})")

                    matching_cards.sort(key=extract_id_number, reverse=True)

                    # 🔧 增强调试：排序后记录优先级顺序
                    logger.info(f"  区域{source_region}→6流转优先级排序（大数值优先）:")
                    for i, (card, _) in enumerate(matching_cards):
                        card_id = self._get_digital_twin_id(card)
                        id_num = extract_id_number((card, source_region))
                        logger.info(f"    优先级{i+1}: {card.get('label')} (ID: {card_id}, 数值: {id_num})")

                    all_source_cards.extend(matching_cards)

            # 🔧 方案1：修改继承策略 - 先完成流转，再按空间位置重新分配ID
            # 按位置排序目标卡牌（从下到上，Y坐标从大到小）
            target_cards.sort(key=lambda card: card.get('bbox', [0,0,0,0])[1], reverse=True)

            # 🔧 执行流转继承，但标记为需要重新分配
            for i, target_card in enumerate(target_cards):
                updated_card = target_card.copy()

                if i < len(all_source_cards):
                    source_card, source_region = all_source_cards[i]
                    source_id = self._get_digital_twin_id(source_card)

                    if source_id:
                        # 临时设置继承的ID（后续会被重新分配）
                        updated_card['twin_id'] = source_id
                        if 'attributes' not in updated_card:
                            updated_card['attributes'] = {}
                        updated_card['attributes']['digital_twin_id'] = source_id
                        updated_card['inherited'] = True
                        updated_card['transition_source'] = f'{source_region}→6'
                        updated_card[f'from_region_{source_region}'] = True
                        updated_card['source_card_id'] = source_id

                        # 🔧 精细化修复：条件化空间重新分配
                        # 计算区域1的源卡牌数量
                        region1_source_cards = [card_tuple for card_tuple in all_source_cards if card_tuple[1] == 1]

                        # 对1→6流转且目标卡牌只有1张的情况，禁用空间重新分配
                        if (source_region == 1 and len(target_cards) == 1):
                            # 1→6流转且只有1张目标卡牌：保持ID继承，不进行空间重新分配
                            updated_card['region6_spatial_reassign'] = False
                            logger.info(f"🔧 1→6流转单张目标保持ID继承: {target_card.get('label', '')} 保持ID {source_id}")
                        else:
                            # 多张目标卡牌或其他源流转：进行空间重新分配
                            updated_card['region6_spatial_reassign'] = True
                            updated_card['spatial_position'] = i + 1  # 从下到上的位置：1, 2, 3...
                            if source_region == 1:
                                logger.info(f"🔧 1→6流转多张目标需要空间重新分配: {target_card.get('label', '')} 临时继承ID {source_id}")
                            else:
                                logger.info(f"🔧 区域{source_region}→6流转需要空间重新分配: {target_card.get('label', '')} 临时继承ID {source_id}")

                        logger.info(f"🔄 区域{source_region}→6流转继承: {target_card.get('label', '')} 继承ID {source_id}，标记重新分配位置{i+1}")
                        updated_cards.append(updated_card)
                    else:
                        logger.warning(f"⚠️ 区域{source_region}卡牌缺少数字孪生ID: {source_card.get('label', 'unknown')}")
                        updated_card[f'from_region_{source_region}'] = True
                        updated_card['source_card_id'] = None
                        updated_cards.append(updated_card)
                else:
                    # 源卡牌不足，标记为需要空间重新分配以获得新ID
                    updated_card = target_card.copy()
                    updated_card['region6_spatial_reassign'] = True
                    updated_card['spatial_position'] = i + 1  # 从下到上的位置
                    updated_card['transition_source'] = 'new_card'
                    logger.info(f"📝 区域6新增卡牌: {target_card.get('label', '')} (源卡牌不足，标记为需要空间重新分配)")
                    updated_cards.append(updated_card)

        # 添加其他非区域6的卡牌
        for card in current_cards:
            if card.get('group_id') != 6 or card.get('label') == '暗':
                updated_cards.append(card)

        # 🔧 方案1：处理区域6的空间重新分配
        updated_cards = self._handle_region6_spatial_reassignment(updated_cards)

        return updated_cards

    def _handle_region6_spatial_reassignment(self, cards: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        处理区域6的空间重新分配

        根据方案1：按空间位置从下到上重新分配ID为1十、2十、3十
        """
        # 找出区域6中需要空间重新分配的卡牌
        region6_reassignment_cards = []
        other_cards = []

        for card in cards:
            if (card.get('group_id') == 6 and
                card.get('region6_spatial_reassign', False)):
                region6_reassignment_cards.append(card)
            else:
                other_cards.append(card)

        if not region6_reassignment_cards:
            return cards

        # 按标签分组处理
        cards_by_label = {}
        for card in region6_reassignment_cards:
            label = card.get('label', '')
            if label not in cards_by_label:
                cards_by_label[label] = []
            cards_by_label[label].append(card)

        # 对每个标签组按空间位置重新分配ID
        for label, label_cards in cards_by_label.items():
            logger.info(f"🎯 区域6空间重新分配: {label} {len(label_cards)}张卡牌")

            # 按空间位置排序（spatial_position已经在流转器中设置）
            label_cards.sort(key=lambda x: x.get('spatial_position', 0))

            # 按从下到上的位置分配ID：1十、2十、3十
            for i, card in enumerate(label_cards):
                new_id = f"{i+1}{label}"

                # 设置新的数字孪生ID
                card['twin_id'] = new_id
                if 'attributes' not in card:
                    card['attributes'] = {}
                card['attributes']['digital_twin_id'] = new_id

                # 移除重新分配标记
                card.pop('region6_spatial_reassign', None)

                logger.info(f"🔄 区域6空间重新分配: {label} 位置{i+1} → ID {new_id}")

        # 将处理后的卡牌添加回其他卡牌
        other_cards.extend(region6_reassignment_cards)
        return other_cards

    def _handle_special_4_to_5_transition(self, current_cards: List[Dict[str, Any]],
                                         previous_cards: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        处理特殊的4→5流转（观战方打牌区→弃牌区）

        支持的流转路径：
        1. 4→5: 观战方打牌区→观战方弃牌区（优先选择较大ID）
        """
        # 获取前一帧区域4的卡牌
        region_4_cards = [card for card in previous_cards if card.get('group_id') == 4]

        # 获取当前帧区域5的卡牌
        region_5_cards = [card for card in current_cards if card.get('group_id') == 5]

        if not region_5_cards:
            return current_cards

        logger.info(f"🔧 开始处理4→5流转: 前一帧区域4有{len(region_4_cards)}张卡牌, 当前帧区域5有{len(region_5_cards)}张卡牌")

        # 处理区域5中的明牌（非暗牌）
        updated_cards = []
        for card in current_cards:
            if card.get('group_id') == 5 and card.get('label') != '暗':
                # 🔧 4→5流转：优先选择较大的ID
                matching_card_4 = self._find_matching_card_in_region_4_with_max_id(card['label'], region_4_cards)
                if matching_card_4:
                    # 实际执行ID继承
                    updated_card = card.copy()
                    source_id = self._get_digital_twin_id(matching_card_4)

                    if source_id:
                        # 设置继承的ID到多个字段以确保兼容性
                        updated_card['twin_id'] = source_id
                        if 'attributes' not in updated_card:
                            updated_card['attributes'] = {}
                        updated_card['attributes']['digital_twin_id'] = source_id
                        updated_card['inherited'] = True
                        updated_card['transition_source'] = '4→5'
                        updated_card['from_region_4'] = True
                        updated_card['source_card_id'] = source_id

                        logger.info(f"🔄 区域4→5流转继承: {card['label']} 继承ID {source_id}")
                        updated_cards.append(updated_card)
                    else:
                        logger.warning(f"⚠️ 区域4卡牌缺少数字孪生ID: {matching_card_4.get('label', 'unknown')}")
                        # 如果源卡牌没有ID，标记但不继承
                        updated_card['from_region_4'] = True
                        updated_card['source_card_id'] = None
                        logger.info(f"🔄 区域4→5流转标记: {card['label']} 来源ID None")
                        updated_cards.append(updated_card)
                else:
                    # 没有找到匹配的区域4卡牌，保持原样
                    updated_cards.append(card)
            else:
                # 非区域5卡牌或暗牌，保持原样
                updated_cards.append(card)

        logger.info(f"🔧 4→5流转处理完成: 更新{len([c for c in updated_cards if c.get('from_region_4')])}张卡牌")
        return updated_cards

    def _find_matching_card_in_region_4_with_max_id(self, target_label: str, region_4_cards: List[Dict[str, Any]]) -> Optional[Dict[str, Any]]:
        """
        在区域4中查找匹配的卡牌，优先选择较大的ID

        Args:
            target_label: 目标标签
            region_4_cards: 区域4的卡牌列表

        Returns:
            匹配的卡牌（优先较大ID），如果没有找到则返回None
        """
        base_label = self._extract_base_label(target_label)

        # 查找相同基础标签的卡牌
        matching_cards = []
        for card in region_4_cards:
            card_base_label = self._extract_base_label(card.get('label', ''))
            if card_base_label == base_label:
                matching_cards.append(card)

        if not matching_cards:
            return None

        # 🔧 按ID倒序排序，优先选择较大的ID（如2九优先于1九）
        def extract_id_number(card):
            twin_id = self._get_digital_twin_id(card) or ''
            if not twin_id or twin_id.startswith('虚拟'):
                return 0
            try:
                return int(twin_id[0])
            except (ValueError, IndexError):
                return 0

        matching_cards.sort(key=extract_id_number, reverse=True)
        selected_card = matching_cards[0]

        logger.info(f"🔧 区域4选择最大ID卡牌: 基础标签='{base_label}', 候选{len(matching_cards)}张, 选中ID='{self._get_digital_twin_id(selected_card)}'")

        return selected_card

    def _find_matching_card_in_region_1(self, label: str, region_1_cards: List[Dict[str, Any]]) -> Optional[Dict[str, Any]]:
        """在区域1中查找相同标签的卡牌，优先选择较大的物理ID"""
        # 提取当前卡牌的基础牌面（去除序号前缀）
        current_base_label = self._extract_base_label(label)

        # 🔧 收集所有匹配的卡牌
        matching_cards = []
        for card in region_1_cards:
            # 提取区域1卡牌的基础牌面
            card_base_label = self._extract_base_label(card.get('label', ''))
            if card_base_label == current_base_label:
                matching_cards.append(card)

        if not matching_cards:
            return None

        # 🔧 套用region2_processor的逻辑：选择最大ID
        def extract_id_number(twin_id: str) -> int:
            """提取ID中的数字部分（如"2二"中的"2"）"""
            if not twin_id or twin_id.startswith('虚拟'):
                return 0
            try:
                return int(twin_id[0])
            except (ValueError, IndexError):
                return 0

        # 选择ID最大的卡牌
        max_card = max(matching_cards, key=lambda card: extract_id_number(
            card.get('attributes', {}).get('digital_twin_id', '') or card.get('twin_id', '')
        ))

        logger.info(f"🔧 区域1选择最大ID卡牌: 基础标签='{current_base_label}', "
                   f"候选{len(matching_cards)}张, 选中ID='{self._get_digital_twin_id(max_card)}'")

        return max_card

    def _find_matching_card_in_region_8(self, label: str, region_8_cards: List[Dict[str, Any]]) -> Optional[Dict[str, Any]]:
        """在区域8中查找相同标签的卡牌"""
        # 提取当前卡牌的基础牌面（去除序号前缀）
        current_base_label = self._extract_base_label(label)

        for card in region_8_cards:
            # 提取区域8卡牌的基础牌面
            card_base_label = self._extract_base_label(card.get('label', ''))
            if card_base_label == current_base_label:
                return card
        return None

    def _handle_special_transitions_to_4(self, current_cards: List[Dict[str, Any]],
                                       previous_cards: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        处理特殊的跨区域流转到区域4（观战方打牌区）

        支持的流转路径（按优先级）：
        1. 2→4: 观战方调整区→观战方打牌区（正常流转）
        2. 1→4: 观战方手牌区→观战方打牌区（跳跃流转，处理抽帧情况）

        重要规则：
        - 区域1流转时优先选择数值大的卡牌（如1二 2二选2二）
        - 区域2流转时按正常顺序选择
        """
        # 获取前一帧各源区域的卡牌
        region_2_cards = [card for card in previous_cards if card.get('group_id') == 2]   # 观战方调整区
        region_1_cards = [card for card in previous_cards if card.get('group_id') == 1]   # 观战方手牌区

        # 获取当前帧区域4的卡牌
        region_4_cards = [card for card in current_cards if card.get('group_id') == 4]

        if not region_4_cards:
            return current_cards

        logger.info(f"🔧 开始处理流转到区域4: 前一帧区域2有{len(region_2_cards)}张卡牌, 区域1有{len(region_1_cards)}张卡牌, 当前帧区域4有{len(region_4_cards)}张卡牌")

        # 🔧 保护机制：检查区域4卡牌是否已经有正确的继承ID
        protected_cards = []
        for card in region_4_cards:
            twin_id = card.get('twin_id')
            if twin_id and card.get('inherited'):
                print(f"🚨🚨🚨 [REGION_4_PROTECTION] 保护已继承的区域4卡牌: 标签'{card.get('label')}', ID'{twin_id}' 🚨🚨🚨")
                protected_cards.append(card)

        if protected_cards:
            print(f"🚨🚨🚨 [REGION_4_PROTECTION] 发现{len(protected_cards)}张已继承的区域4卡牌，跳过流转处理 🚨🚨🚨")
            return current_cards

        # 按标签分组区域4的卡牌
        from collections import defaultdict
        cards_by_label = defaultdict(list)
        for card in region_4_cards:
            if card.get('label') != '暗':  # 只处理明牌
                cards_by_label[card['label']].append(card)

        updated_cards = []

        # 处理每种标签的卡牌
        for label, target_cards in cards_by_label.items():
            # 🔧 第一优先级：2→4流转
            matching_cards_2 = self._find_matching_cards_by_label(label, region_2_cards, source_region=2)

            if matching_cards_2:
                # 找到区域2的匹配卡牌，执行2→4流转
                logger.info(f"🔄 执行2→4流转: {label}, 找到{len(matching_cards_2)}张源卡牌")

                for i, target_card in enumerate(target_cards):
                    if i < len(matching_cards_2):
                        source_card = matching_cards_2[i]
                        source_id = self._get_digital_twin_id(source_card)

                        if source_id:
                            updated_card = target_card.copy()
                            updated_card['twin_id'] = source_id
                            if 'attributes' not in updated_card:
                                updated_card['attributes'] = {}
                            updated_card['attributes']['digital_twin_id'] = source_id
                            updated_card['inherited'] = True
                            updated_card['transition_source'] = '2→4'
                            updated_card['from_region_2'] = True
                            updated_card['source_card_id'] = source_id

                            logger.info(f"🔄 区域2→4流转继承: {label} 继承ID {source_id}")
                            updated_cards.append(updated_card)
                        else:
                            # 源卡牌没有ID，保持原样
                            updated_cards.append(target_card)
                    else:
                        # 源卡牌不足，保持原样
                        updated_cards.append(target_card)
            else:
                # 🔧 第二优先级：1→4流转（处理抽帧情况）
                matching_cards_1 = self._find_matching_cards_by_label(label, region_1_cards, source_region=1)

                if matching_cards_1:
                    logger.info(f"🔄 执行1→4跳跃流转: {label}, 找到{len(matching_cards_1)}张源卡牌（抽帧场景，优先选择大数值）")

                    for i, target_card in enumerate(target_cards):
                        if i < len(matching_cards_1):
                            source_card = matching_cards_1[i]
                            source_id = self._get_digital_twin_id(source_card)

                            if source_id:
                                updated_card = target_card.copy()
                                updated_card['twin_id'] = source_id
                                if 'attributes' not in updated_card:
                                    updated_card['attributes'] = {}
                                updated_card['attributes']['digital_twin_id'] = source_id
                                updated_card['inherited'] = True
                                updated_card['transition_source'] = '1→4'
                                updated_card['from_region_1'] = True
                                updated_card['source_card_id'] = source_id
                                updated_card['skip_frame_transition'] = True  # 标记为跳帧流转

                                logger.info(f"🔄 区域1→4跳跃流转继承: {label} 继承ID {source_id}（抽帧场景，选择大数值卡牌）")
                                updated_cards.append(updated_card)
                            else:
                                # 源卡牌没有ID，保持原样
                                updated_cards.append(target_card)
                        else:
                            # 源卡牌不足，保持原样
                            updated_cards.append(target_card)
                else:
                    # 无法从区域2或区域1继承，保持原样（交由SimpleInheritor或ID分配器处理）
                    for target_card in target_cards:
                        logger.info(f"📝 区域4卡牌: {label} (无法从区域2或1继承，交由后续处理)")
                        updated_cards.append(target_card)

        # 添加其他非区域4的卡牌
        for card in current_cards:
            if card.get('group_id') != 4 or card.get('label') == '暗':
                updated_cards.append(card)

        logger.info(f"🔧 流转到区域4处理完成: 更新{len([c for c in updated_cards if c.get('from_region_2') or c.get('from_region_1')])}张卡牌")
        return updated_cards

    def _find_matching_cards_by_label(self, label: str, source_cards: List[Dict[str, Any]], source_region: int) -> List[Dict[str, Any]]:
        """在源卡牌中查找匹配指定标签的卡牌，根据源区域采用不同的排序策略"""
        matching_cards = []
        for card in source_cards:
            if card.get('label') == label:
                matching_cards.append(card)

        def extract_id_number(card):
            twin_id = self._get_digital_twin_id(card) or ''
            if not twin_id or twin_id.startswith('虚拟'):
                return 0 if source_region == 1 else 999  # 区域1时虚拟ID排在最前，其他区域排在最后
            try:
                return int(twin_id[0])
            except (ValueError, IndexError):
                return 0 if source_region == 1 else 999

        if source_region == 1:
            # 🔧 区域1流转：优先选择数值大的卡牌（如1二 2二选2二）
            matching_cards.sort(key=extract_id_number, reverse=True)
            logger.info(f"区域1流转排序: {label} 按ID倒序排列，优先选择大数值卡牌")
        else:
            # 🔧 其他区域流转：按数值从小到大排序（保持原有逻辑）
            matching_cards.sort(key=extract_id_number)
            logger.info(f"区域{source_region}流转排序: {label} 按ID正序排列")

        return matching_cards

def create_region_transitioner():
    """创建区域流转器"""
    return RegionTransitioner()
